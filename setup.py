#!/usr/bin/env python3
"""
Setup script for PDF to Excel Converter
This script installs dependencies and checks the environment
"""

import sys
import subprocess
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # Core dependencies
    dependencies = [
        "PyPDF2>=3.0.0",
        "pandas>=1.5.0", 
        "openpyxl>=3.0.0",
        "PySide6>=6.0.0",
        "pdfplumber>=0.7.0"
    ]
    
    failed_packages = []
    
    for package in dependencies:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed successfully")
        else:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        print("Try installing manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    
    print("✅ All dependencies installed successfully!")
    return True

def test_imports():
    """Test if all required modules can be imported"""
    print("\n🧪 Testing imports...")
    
    modules = [
        ("PyPDF2", "PyPDF2"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("PySide6", "PySide6.QtWidgets"),
        ("pdfplumber", "pdfplumber")
    ]
    
    failed_imports = []
    
    for name, module in modules:
        try:
            __import__(module)
            print(f"✅ {name} import successful")
        except ImportError as e:
            print(f"❌ {name} import failed: {e}")
            failed_imports.append(name)
    
    if failed_imports:
        print(f"\n❌ Import failures: {', '.join(failed_imports)}")
        return False
    
    print("✅ All imports successful!")
    return True

def main():
    """Main setup function"""
    print("🚀 PDF to Excel Converter Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Test imports
    if not test_imports():
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nYou can now run the converter:")
    print("  python pdf_to_excel.py")
    print("\nOr test PDF extraction:")
    print("  python test_pdf_extraction.py your_file.pdf")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
