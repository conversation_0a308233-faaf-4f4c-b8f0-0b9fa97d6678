@echo off
echo PDF Converter - Safe Testing Suite
echo ===================================
echo.

if "%1"=="" (
    echo Usage: run_safe_tests.bat your_pdf_file.pdf
    echo.
    echo This script will run a series of safe tests to identify
    echo where the crash occurs and find a working solution.
    pause
    exit /b 1
)

set PDF_FILE=%1

echo Testing PDF file: %PDF_FILE%
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

echo Step 1: Installing required packages...
echo =====================================
python -m pip install pdfplumber pandas openpyxl --quiet
if errorlevel 1 (
    echo Warning: Some packages may not have installed correctly
)
echo.

echo Step 2: Running minimal test (safest)...
echo ========================================
python minimal_test.py "%PDF_FILE%"
if errorlevel 1 (
    echo.
    echo Minimal test failed. The PDF may have format issues.
    echo Check the output above for specific errors.
    pause
    exit /b 1
)
echo.

echo Step 3: Running safe converter with 5 pages...
echo ==============================================
python safe_pdf_converter.py "%PDF_FILE%" 5
if errorlevel 1 (
    echo.
    echo Safe converter failed with 5 pages.
    echo This suggests a fundamental compatibility issue.
    pause
    exit /b 1
)
echo.

echo Step 4: Running safe converter with 20 pages...
echo ===============================================
python safe_pdf_converter.py "%PDF_FILE%" 20
if errorlevel 1 (
    echo.
    echo Safe converter failed with 20 pages.
    echo The issue may be related to memory or data volume.
    pause
    exit /b 1
)
echo.

echo Step 5: Running safe converter with 50 pages...
echo ===============================================
python safe_pdf_converter.py "%PDF_FILE%" 50
if errorlevel 1 (
    echo.
    echo Safe converter failed with 50 pages.
    echo Recommend using smaller batches.
    pause
    exit /b 1
)
echo.

echo All tests completed successfully!
echo =================================
echo.
echo Your PDF appears to be compatible. You can now try:
echo.
echo 1. Full conversion with safe converter:
echo    python safe_pdf_converter.py "%PDF_FILE%"
echo.
echo 2. Or use the large PDF converter:
echo    python large_pdf_converter.py "%PDF_FILE%"
echo.
echo 3. Or try the GUI version:
echo    python pdf_to_excel.py
echo.
pause
