#!/usr/bin/env python3
"""
Quick fix for the GBK-EUC-H encoding issue with PyPDF2
This script demonstrates how to handle the encoding warning and memory access violation
"""

import sys
import os
import warnings
import PyPDF2

def test_pdf_with_encoding_fix(pdf_path):
    """Test PDF reading with encoding issue fixes"""
    print(f"🔧 Testing PDF with encoding fixes: {pdf_path}")
    print("=" * 60)
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return False
    
    # Method 1: Suppress warnings and use strict=False
    try:
        print("Method 1: PyPDF2 with warnings suppressed...")
        
        # Suppress the specific encoding warning
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            warnings.filterwarnings("ignore", message=".*Advanced encoding.*")
            warnings.filterwarnings("ignore", message=".*GBK-EUC-H.*")
            
            with open(pdf_path, 'rb') as file:
                # Use strict=False to handle problematic PDFs
                pdf_reader = PyPDF2.PdfReader(file, strict=False)
                
                print(f"📄 PDF has {len(pdf_reader.pages)} pages")
                
                # Test reading first page
                if len(pdf_reader.pages) > 0:
                    try:
                        first_page = pdf_reader.pages[0]
                        text = first_page.extract_text()
                        print(f"✅ Successfully extracted {len(text)} characters from first page")
                        print(f"📝 First 200 characters: {repr(text[:200])}")
                        return True
                    except Exception as e:
                        print(f"❌ Error extracting text from first page: {e}")
                        return False
                else:
                    print("❌ PDF has no pages")
                    return False
                    
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
    
    # Method 2: Try pdfplumber as alternative
    try:
        print("\nMethod 2: Using pdfplumber...")
        import pdfplumber
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"📄 PDF has {len(pdf.pages)} pages")
            
            if len(pdf.pages) > 0:
                first_page = pdf.pages[0]
                text = first_page.extract_text()
                if text:
                    print(f"✅ Successfully extracted {len(text)} characters from first page")
                    print(f"📝 First 200 characters: {repr(text[:200])}")
                    return True
                else:
                    print("❌ No text extracted from first page")
                    return False
            else:
                print("❌ PDF has no pages")
                return False
                
    except ImportError:
        print("❌ pdfplumber not installed. Install with: pip install pdfplumber")
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
    
    return False

def install_pdfplumber():
    """Install pdfplumber if not available"""
    try:
        import pdfplumber
        print("✅ pdfplumber is already installed")
        return True
    except ImportError:
        print("📦 Installing pdfplumber...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdfplumber"])
            print("✅ pdfplumber installed successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to install pdfplumber: {e}")
            return False

def main():
    """Main function"""
    print("🔧 PDF Encoding Issue Fix Tool")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("Usage: python fix_encoding_issue.py <pdf_file>")
        print("Example: python fix_encoding_issue.py sample_data.pdf")
        return
    
    pdf_file = sys.argv[1]
    
    # Install pdfplumber if needed
    install_pdfplumber()
    
    # Test the PDF
    success = test_pdf_with_encoding_fix(pdf_file)
    
    if success:
        print("\n✅ PDF can be read successfully!")
        print("The main converter should now work with this PDF.")
    else:
        print("\n❌ PDF reading failed with all methods.")
        print("This PDF might be:")
        print("  - Image-based (scanned document)")
        print("  - Corrupted")
        print("  - Using unsupported encoding")
        print("  - Password protected")

if __name__ == "__main__":
    main()
