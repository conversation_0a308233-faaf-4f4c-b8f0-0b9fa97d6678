#!/usr/bin/env python3
"""
Large PDF to Excel Converter
Optimized for handling very large PDFs (400+ pages) without memory issues
"""

import sys
import os
import warnings
import pandas as pd
import re
from pathlib import Path
import gc

def extract_text_safely(pdf_path):
    """Extract text from PDF with memory management"""
    print(f"📖 Extracting text from: {pdf_path}")
    
    # Try pdfplumber first (more reliable)
    try:
        import pdfplumber
        print("Using pdfplumber for extraction...")
        
        page_texts = []
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            print(f"📄 PDF has {total_pages} pages")
            
            for page_num, page in enumerate(pdf.pages):
                try:
                    text = page.extract_text()
                    if text and text.strip():
                        page_texts.append(text)
                    
                    if (page_num + 1) % 50 == 0:
                        print(f"Extracted page {page_num + 1}/{total_pages}")
                        
                except Exception as e:
                    print(f"Error on page {page_num + 1}: {e}")
                    continue
        
        return page_texts
        
    except ImportError:
        print("pdfplumber not available, trying PyPDF2...")
    except Exception as e:
        print(f"pdfplumber failed: {e}")
    
    # Fallback to PyPDF2
    try:
        import PyPDF2
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore")
            
            page_texts = []
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file, strict=False)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        text = page.extract_text()
                        if text and text.strip():
                            page_texts.append(text)
                        
                        if (page_num + 1) % 50 == 0:
                            print(f"Extracted page {page_num + 1}/{len(pdf_reader.pages)}")
                            
                    except Exception as e:
                        print(f"Error on page {page_num + 1}: {e}")
                        continue
            
            return page_texts
            
    except Exception as e:
        print(f"PyPDF2 failed: {e}")
        return []

def process_data_streaming(page_texts, output_path):
    """Process data in streaming fashion to handle large datasets"""
    print(f"🔄 Processing {len(page_texts)} pages of data...")
    
    # Use a temporary file to store data as we process it
    temp_file = str(Path(output_path).with_suffix('.temp.csv'))
    
    total_data_points = 0
    batch_size = 1000  # Write every 1000 data points
    current_batch = []
    
    try:
        # Process pages in batches
        for page_num, text in enumerate(page_texts):
            if (page_num + 1) % 50 == 0:
                print(f"Processing page {page_num + 1}/{len(page_texts)} ({total_data_points:,} data points so far)")
            
            lines = text.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # Skip empty lines and headers
                if not line or 'Time1' in line or 'Data Records' in line or 'Printed on:' in line or 'C1:PJX5202' in line:
                    continue
                
                # Extract data using regex
                pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
                matches = re.findall(pattern, line)
                
                for match in matches:
                    timestamp_str = match[0]
                    sensor_value = float(match[1])
                    full_timestamp = f"2025-{timestamp_str}"
                    
                    current_batch.append({
                        'Time1': full_timestamp,
                        'C1_PJX5202_g': sensor_value
                    })
                    
                    total_data_points += 1
                    
                    # Write batch to file when it reaches batch_size
                    if len(current_batch) >= batch_size:
                        write_batch_to_file(current_batch, temp_file, total_data_points == len(current_batch))
                        current_batch = []
                        
                        # Force garbage collection
                        if total_data_points % 10000 == 0:
                            gc.collect()
        
        # Write remaining data
        if current_batch:
            write_batch_to_file(current_batch, temp_file, False)
        
        print(f"✅ Extracted {total_data_points:,} data points")
        
        # Convert CSV to Excel
        if total_data_points > 0:
            convert_csv_to_excel(temp_file, output_path, total_data_points)
            
            # Clean up temp file
            if os.path.exists(temp_file):
                os.remove(temp_file)
            
            return True
        else:
            print("❌ No data found")
            return False
            
    except Exception as e:
        print(f"❌ Error processing data: {e}")
        return False

def write_batch_to_file(batch, temp_file, is_first_batch):
    """Write a batch of data to temporary CSV file"""
    df_batch = pd.DataFrame(batch)
    
    # Convert timestamp
    df_batch['Time1'] = pd.to_datetime(df_batch['Time1'], format='%Y-%m-%d %H:%M:%S')
    
    # Write to CSV (append mode after first batch)
    mode = 'w' if is_first_batch else 'a'
    header = is_first_batch
    
    df_batch.to_csv(temp_file, mode=mode, header=header, index=False)

def convert_csv_to_excel(csv_file, excel_file, total_points):
    """Convert CSV to Excel with formatting"""
    print(f"📊 Converting to Excel format...")
    
    try:
        # Read CSV in chunks to manage memory
        chunk_size = 50000
        chunks = []
        
        for chunk in pd.read_csv(csv_file, chunksize=chunk_size):
            chunk['Time1'] = pd.to_datetime(chunk['Time1'])
            chunks.append(chunk)
        
        # Combine chunks
        df = pd.concat(chunks, ignore_index=True)
        df = df.sort_values('Time1').reset_index(drop=True)
        
        # Add analysis columns
        df['Date'] = df['Time1'].dt.date
        df['Time_Only'] = df['Time1'].dt.time
        df['Hour'] = df['Time1'].dt.hour
        
        # Create Excel file
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # Main data (limit to 1M rows for Excel compatibility)
            if len(df) > 1000000:
                print(f"⚠️  Dataset has {len(df):,} rows. Excel limit is 1M rows. Saving first 1M rows.")
                df_excel = df.head(1000000)
            else:
                df_excel = df
            
            df_excel.to_excel(writer, sheet_name='Sensor_Data', index=False)
            
            # Summary statistics
            summary_stats = {
                'Statistic': ['Total Points', 'Date Range Start', 'Date Range End', 'Min Value', 'Max Value', 'Average'],
                'Value': [
                    len(df),
                    df['Time1'].min(),
                    df['Time1'].max(),
                    df['C1_PJX5202_g'].min(),
                    df['C1_PJX5202_g'].max(),
                    df['C1_PJX5202_g'].mean()
                ]
            }
            
            summary_df = pd.DataFrame(summary_stats)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        print(f"✅ Excel file created: {excel_file}")
        print(f"📊 Total data points: {len(df):,}")
        print(f"📅 Date range: {df['Time1'].min()} to {df['Time1'].max()}")
        print(f"📈 Value range: {df['C1_PJX5202_g'].min():.3f}g to {df['C1_PJX5202_g'].max():.3f}g")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating Excel file: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Large PDF to Excel Converter")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("Usage: python large_pdf_converter.py <pdf_file> [output_file]")
        return
    
    pdf_file = sys.argv[1]
    excel_file = sys.argv[2] if len(sys.argv) > 2 else str(Path(pdf_file).with_suffix('.xlsx'))
    
    if not os.path.exists(pdf_file):
        print(f"❌ File not found: {pdf_file}")
        return
    
    print(f"📄 Input: {pdf_file}")
    print(f"📊 Output: {excel_file}")
    
    # Extract text
    page_texts = extract_text_safely(pdf_file)
    
    if not page_texts:
        print("❌ Could not extract any text from PDF")
        return
    
    # Process data
    success = process_data_streaming(page_texts, excel_file)
    
    if success:
        print(f"\n🎉 Conversion completed successfully!")
        print(f"💾 Output saved to: {excel_file}")
    else:
        print(f"\n❌ Conversion failed")

if __name__ == "__main__":
    main()
