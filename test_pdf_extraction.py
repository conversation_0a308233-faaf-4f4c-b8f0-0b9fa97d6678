#!/usr/bin/env python3
"""
Test script for PDF data extraction
This script helps debug and test the PDF extraction functionality
"""

import sys
import os
import PyPDF2
import pandas as pd
import re
from pathlib import Path

def test_pdf_extraction(pdf_path):
    """Test PDF extraction and show detailed output"""
    print(f"🔍 Testing PDF extraction for: {pdf_path}")
    print("=" * 60)
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return
    
    timestamps = []
    sensor_values = []
    
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            print(f"📄 PDF has {len(pdf_reader.pages)} pages")
            
            for page_num, page in enumerate(pdf_reader.pages):
                print(f"\n📖 Processing page {page_num + 1}...")
                
                # Extract text from the page
                text = page.extract_text()
                print(f"📝 Extracted text length: {len(text)} characters")
                
                # Show first 500 characters of extracted text
                print(f"📋 First 500 characters of extracted text:")
                print("-" * 40)
                print(repr(text[:500]))
                print("-" * 40)
                
                lines = text.split('\n')
                print(f"📄 Total lines: {len(lines)}")
                
                # Show all lines for debugging
                print(f"\n🔍 All lines from page {page_num + 1}:")
                for i, line in enumerate(lines):
                    line = line.strip()
                    if line:
                        print(f"Line {i+1:3d}: '{line}'")
                
                # Process each line to extract data
                data_found_on_page = 0
                for line_num, line in enumerate(lines):
                    line = line.strip()
                    
                    # Skip empty lines and headers
                    if not line or 'Time1' in line or 'Data Records' in line or 'Printed on:' in line or 'C1:PJX5202' in line:
                        continue
                    
                    # Primary pattern to match timestamp and sensor value
                    pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
                    matches = re.findall(pattern, line)
                    
                    if matches:
                        for match in matches:
                            timestamp_str = match[0]
                            sensor_value = float(match[1])
                            full_timestamp = f"2025-{timestamp_str}"
                            timestamps.append(full_timestamp)
                            sensor_values.append(sensor_value)
                            data_found_on_page += 1
                            print(f"✅ Found data: {full_timestamp} -> {sensor_value}g")
                    else:
                        # Alternative pattern for different formatting
                        tab_pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*\t*\s*(-?\d+\.\d+)\s*g'
                        tab_matches = re.findall(tab_pattern, line)
                        
                        if tab_matches:
                            for match in tab_matches:
                                timestamp_str = match[0]
                                sensor_value = float(match[1])
                                full_timestamp = f"2025-{timestamp_str}"
                                timestamps.append(full_timestamp)
                                sensor_values.append(sensor_value)
                                data_found_on_page += 1
                                print(f"✅ Found data (alt pattern): {full_timestamp} -> {sensor_value}g")
                        else:
                            # Show lines that didn't match for debugging
                            if any(char.isdigit() for char in line):
                                print(f"❓ No match for line {line_num+1}: '{line}'")
                
                print(f"📊 Data points found on page {page_num + 1}: {data_found_on_page}")
        
        print(f"\n📈 SUMMARY:")
        print(f"Total data points extracted: {len(timestamps)}")
        
        if timestamps and sensor_values:
            print(f"First timestamp: {timestamps[0]}")
            print(f"Last timestamp: {timestamps[-1]}")
            print(f"First sensor value: {sensor_values[0]}g")
            print(f"Last sensor value: {sensor_values[-1]}g")
            print(f"Min sensor value: {min(sensor_values)}g")
            print(f"Max sensor value: {max(sensor_values)}g")
            print(f"Average sensor value: {sum(sensor_values)/len(sensor_values):.3f}g")
            
            # Create a sample DataFrame
            df = pd.DataFrame({
                'Time1': timestamps,
                'C1_PJX5202_g': sensor_values
            })
            
            # Show first few rows
            print(f"\n📋 First 10 rows of extracted data:")
            print(df.head(10).to_string(index=False))
            
            # Save to Excel for verification
            output_path = str(Path(pdf_path).with_suffix('_test_output.xlsx'))
            df.to_excel(output_path, index=False)
            print(f"\n💾 Test output saved to: {output_path}")
            
        else:
            print("❌ No data was extracted from the PDF")
            
    except Exception as e:
        print(f"❌ Error processing PDF: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python test_pdf_extraction.py <pdf_file>")
        print("Example: python test_pdf_extraction.py sample_data.pdf")
        return
    
    pdf_file = sys.argv[1]
    test_pdf_extraction(pdf_file)

if __name__ == "__main__":
    main()
