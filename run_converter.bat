@echo off
echo PDF to Excel Converter
echo =====================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Run setup to install dependencies
echo Running setup...
python setup.py
if errorlevel 1 (
    echo ERROR: Setup failed
    pause
    exit /b 1
)

echo.
echo Starting PDF to Excel Converter...
echo.
python pdf_to_excel.py

echo.
echo Converter finished.
pause
