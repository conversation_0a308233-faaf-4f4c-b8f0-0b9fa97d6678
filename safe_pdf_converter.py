#!/usr/bin/env python3
"""
Ultra-Safe PDF to Excel Converter
Designed to avoid all potential crash points and handle large PDFs safely
"""

import sys
import os
import warnings
import traceback
import gc
from pathlib import Path

# Suppress all warnings to prevent issues
warnings.filterwarnings("ignore")

def safe_import_check():
    """Safely check and import required libraries"""
    required_libs = {}
    
    try:
        import pandas as pd
        required_libs['pandas'] = pd
        print("✅ pandas imported")
    except ImportError as e:
        print(f"❌ pandas not available: {e}")
        return None
    
    try:
        import pdfplumber
        required_libs['pdfplumber'] = pdfplumber
        print("✅ pdfplumber imported")
    except ImportError:
        print("⚠️  pdfplumber not available, trying PyPDF2...")
        try:
            import PyPDF2
            required_libs['PyPDF2'] = PyPDF2
            print("✅ PyPDF2 imported")
        except ImportError as e:
            print(f"❌ No PDF library available: {e}")
            return None
    
    return required_libs

def extract_single_page_safe(pdf_obj, page_num, lib_type):
    """Safely extract text from a single page"""
    try:
        if lib_type == 'pdfplumber':
            page = pdf_obj.pages[page_num]
            text = page.extract_text()
        else:  # PyPDF2
            page = pdf_obj.pages[page_num]
            text = page.extract_text()
        
        return text if text else ""
    except Exception as e:
        print(f"Error extracting page {page_num + 1}: {e}")
        return ""

def process_pdf_ultra_safe(pdf_path, max_pages=None):
    """Ultra-safe PDF processing with maximum error protection"""
    print(f"🔒 Ultra-safe processing: {pdf_path}")
    
    libs = safe_import_check()
    if not libs:
        return None, "Required libraries not available"
    
    if not os.path.exists(pdf_path):
        return None, f"File not found: {pdf_path}"
    
    # Check file size
    try:
        file_size = os.path.getsize(pdf_path)
        print(f"📄 File size: {file_size:,} bytes")
        if file_size > 100 * 1024 * 1024:  # 100MB
            print("⚠️  Large file detected, processing with extra caution")
    except:
        pass
    
    all_data = []
    
    # Try pdfplumber first
    if 'pdfplumber' in libs:
        try:
            print("🔍 Using pdfplumber...")
            with libs['pdfplumber'].open(pdf_path) as pdf:
                total_pages = len(pdf.pages)
                print(f"📄 Total pages: {total_pages}")
                
                # Limit pages if specified
                pages_to_process = min(max_pages or total_pages, total_pages)
                if max_pages and max_pages < total_pages:
                    print(f"⚠️  Processing only first {max_pages} pages")
                
                for page_num in range(pages_to_process):
                    try:
                        # Process one page at a time with full error isolation
                        text = extract_single_page_safe(pdf, page_num, 'pdfplumber')
                        
                        if text:
                            page_data = extract_data_from_text_safe(text, page_num + 1)
                            if page_data:
                                all_data.extend(page_data)
                        
                        # Progress and memory management
                        if (page_num + 1) % 10 == 0:
                            print(f"Processed {page_num + 1}/{pages_to_process} pages, {len(all_data)} data points")
                            gc.collect()  # Force garbage collection
                        
                        # Safety limit
                        if len(all_data) > 500000:  # 500k data points max
                            print("⚠️  Reached safety limit of 500k data points")
                            break
                            
                    except Exception as e:
                        print(f"Error on page {page_num + 1}: {e}")
                        continue
                
                return all_data, None
                
        except Exception as e:
            print(f"pdfplumber failed: {e}")
    
    # Fallback to PyPDF2
    if 'PyPDF2' in libs:
        try:
            print("🔍 Fallback to PyPDF2...")
            with open(pdf_path, 'rb') as file:
                pdf_reader = libs['PyPDF2'].PdfReader(file, strict=False)
                total_pages = len(pdf_reader.pages)
                
                pages_to_process = min(max_pages or total_pages, total_pages)
                
                for page_num in range(pages_to_process):
                    try:
                        text = extract_single_page_safe(pdf_reader, page_num, 'PyPDF2')
                        
                        if text:
                            page_data = extract_data_from_text_safe(text, page_num + 1)
                            if page_data:
                                all_data.extend(page_data)
                        
                        if (page_num + 1) % 10 == 0:
                            print(f"Processed {page_num + 1}/{pages_to_process} pages")
                            gc.collect()
                        
                        if len(all_data) > 500000:
                            break
                            
                    except Exception as e:
                        print(f"Error on page {page_num + 1}: {e}")
                        continue
                
                return all_data, None
                
        except Exception as e:
            print(f"PyPDF2 failed: {e}")
    
    return None, "All extraction methods failed"

def extract_data_from_text_safe(text, page_num):
    """Safely extract data from text with maximum error protection"""
    try:
        import re
        
        data_points = []
        lines = text.split('\n')
        
        # Limit lines to prevent memory issues
        if len(lines) > 1000:
            lines = lines[:1000]
        
        pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
        
        for line in lines:
            try:
                line = line.strip()
                
                if not line or 'Time1' in line or 'Data Records' in line:
                    continue
                
                matches = re.findall(pattern, line)
                
                for match in matches:
                    try:
                        timestamp_str = match[0]
                        sensor_value = float(match[1])
                        full_timestamp = f"2025-{timestamp_str}"
                        
                        data_points.append({
                            'Time1': full_timestamp,
                            'C1_PJX5202_g': sensor_value,
                            'Page': page_num
                        })
                        
                        # Safety limit per page
                        if len(data_points) > 1000:
                            break
                            
                    except (ValueError, IndexError) as e:
                        continue
                        
            except Exception as e:
                continue
        
        return data_points
        
    except Exception as e:
        print(f"Error extracting data from page {page_num}: {e}")
        return []

def create_excel_safe(data, output_path):
    """Safely create Excel file"""
    try:
        import pandas as pd
        
        if not data:
            return False, "No data to save"
        
        print(f"📊 Creating Excel with {len(data)} data points...")
        
        # Create DataFrame safely
        df = pd.DataFrame(data)
        
        # Convert timestamp safely
        try:
            df['Time1'] = pd.to_datetime(df['Time1'], format='%Y-%m-%d %H:%M:%S')
        except Exception as e:
            print(f"Warning: Could not convert timestamps: {e}")
        
        # Sort safely
        try:
            df = df.sort_values('Time1').reset_index(drop=True)
        except:
            pass
        
        # Save to Excel
        df.to_excel(output_path, index=False)
        
        print(f"✅ Excel file created: {output_path}")
        print(f"📊 Data points: {len(df):,}")
        
        if 'Time1' in df.columns and df['Time1'].dtype != 'object':
            print(f"📅 Date range: {df['Time1'].min()} to {df['Time1'].max()}")
        
        if 'C1_PJX5202_g' in df.columns:
            print(f"📈 Value range: {df['C1_PJX5202_g'].min():.3f}g to {df['C1_PJX5202_g'].max():.3f}g")
        
        return True, None
        
    except Exception as e:
        return False, f"Error creating Excel: {e}"

def main():
    """Main function with comprehensive error handling"""
    try:
        print("🔒 Ultra-Safe PDF to Excel Converter")
        print("=" * 50)
        
        if len(sys.argv) < 2:
            print("Usage: python safe_pdf_converter.py <pdf_file> [max_pages]")
            print("Example: python safe_pdf_converter.py data.pdf 50")
            return
        
        pdf_file = sys.argv[1]
        max_pages = int(sys.argv[2]) if len(sys.argv) > 2 else None
        
        excel_file = str(Path(pdf_file).with_suffix('.xlsx'))
        
        print(f"📄 Input: {pdf_file}")
        print(f"📊 Output: {excel_file}")
        if max_pages:
            print(f"📑 Max pages: {max_pages}")
        
        # Process PDF
        data, error = process_pdf_ultra_safe(pdf_file, max_pages)
        
        if error:
            print(f"❌ Error: {error}")
            return
        
        if not data:
            print("❌ No data extracted")
            return
        
        # Create Excel
        success, error = create_excel_safe(data, excel_file)
        
        if success:
            print(f"\n🎉 Conversion completed successfully!")
        else:
            print(f"❌ Error creating Excel: {error}")
    
    except KeyboardInterrupt:
        print("\n⚠️  Conversion interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
