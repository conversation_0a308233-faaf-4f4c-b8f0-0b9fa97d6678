#!/usr/bin/env python3
"""
Quick test script to check if PDF can be processed without crashing
"""

import sys
import os
import warnings
import gc

def quick_pdf_test(pdf_path):
    """Quick test to see if PDF can be processed safely"""
    print(f"🧪 Quick test for: {pdf_path}")
    print("=" * 50)
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return False
    
    # Test 1: Basic file access
    try:
        with open(pdf_path, 'rb') as f:
            size = len(f.read())
        print(f"✅ File readable, size: {size:,} bytes")
    except Exception as e:
        print(f"❌ Cannot read file: {e}")
        return False
    
    # Test 2: Extract first few pages only
    try:
        import pdfplumber
        print("🔍 Testing pdfplumber extraction (first 5 pages)...")
        
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            print(f"📄 Total pages: {total_pages}")
            
            # Test only first 5 pages
            test_pages = min(5, total_pages)
            extracted_text = []
            
            for i in range(test_pages):
                try:
                    page = pdf.pages[i]
                    text = page.extract_text()
                    if text:
                        extracted_text.append(text)
                        print(f"✅ Page {i+1}: {len(text)} characters")
                    else:
                        print(f"⚠️  Page {i+1}: No text")
                except Exception as e:
                    print(f"❌ Page {i+1}: Error - {e}")
            
            if extracted_text:
                print(f"✅ Successfully extracted text from {len(extracted_text)} pages")
                
                # Test data extraction from first page
                first_page_text = extracted_text[0]
                lines = first_page_text.split('\n')
                print(f"📝 First page has {len(lines)} lines")
                
                # Show first few lines
                print("📋 First 10 lines:")
                for i, line in enumerate(lines[:10]):
                    print(f"  {i+1:2d}: {repr(line)}")
                
                # Test regex pattern
                import re
                pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
                
                data_found = 0
                for line in lines:
                    matches = re.findall(pattern, line.strip())
                    if matches:
                        data_found += len(matches)
                        if data_found <= 3:  # Show first 3 matches
                            for match in matches:
                                print(f"📊 Found data: {match[0]} -> {match[1]}g")
                
                print(f"✅ Found {data_found} data points in first page")
                
                if data_found > 0:
                    print("🎉 PDF format is compatible!")
                    return True
                else:
                    print("⚠️  No data found in expected format")
                    return False
            else:
                print("❌ No text extracted")
                return False
                
    except ImportError:
        print("❌ pdfplumber not installed")
        return False
    except Exception as e:
        print(f"❌ pdfplumber test failed: {e}")
        return False

def memory_test():
    """Test current memory usage"""
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"💾 Current memory usage: {memory_mb:.1f} MB")
        return memory_mb
    except ImportError:
        print("💾 psutil not available for memory monitoring")
        return 0

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python quick_test.py <pdf_file>")
        return
    
    pdf_file = sys.argv[1]
    
    print("🚀 PDF Quick Test")
    print("=" * 30)
    
    # Check initial memory
    initial_memory = memory_test()
    
    # Run test
    success = quick_pdf_test(pdf_file)
    
    # Check final memory
    final_memory = memory_test()
    
    if initial_memory > 0 and final_memory > 0:
        memory_increase = final_memory - initial_memory
        print(f"📈 Memory increase: {memory_increase:.1f} MB")
    
    # Force garbage collection
    gc.collect()
    
    if success:
        print("\n✅ Quick test PASSED")
        print("The PDF should work with the converter")
        print("\nNext steps:")
        print("1. For large PDFs (400+ pages), use: python large_pdf_converter.py your_file.pdf")
        print("2. For smaller PDFs, use: python pdf_to_excel.py")
    else:
        print("\n❌ Quick test FAILED")
        print("There may be issues with this PDF format")

if __name__ == "__main__":
    main()
