# PDF to Excel Converter

A Python application that extracts sensor data from PDF files and converts them to Excel format. Specifically designed to handle data tables with timestamps and sensor readings.

## Features

- **GUI Interface**: Easy-to-use graphical interface for file selection
- **Command Line Support**: Can be run from command line for automation
- **Multiple PDF Extraction Methods**: Uses PyPDF2 and pdfplumber for better compatibility
- **Data Analysis**: Automatically calculates statistics and trends
- **Excel Formatting**: Creates well-formatted Excel files with multiple sheets
- **Progress Tracking**: Shows conversion progress with detailed status updates

## Installation

1. **Install Python 3.7 or higher**

2. **Install required dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

   Or install manually:
   ```bash
   pip install PyPDF2 pandas openpyxl PySide6 pdfplumber
   ```

## Usage

### GUI Mode (Recommended)

Simply run the script:
```bash
python pdf_to_excel.py
```

This will open a file dialog where you can:
1. Select your PDF file
2. The converter will automatically process the PDF
3. Excel file will be saved in the same location as the PDF
4. You'll get a summary of the conversion results

### Command Line Mode

```bash
python pdf_to_excel.py input.pdf [output.xlsx]
```

Examples:
```bash
# Convert PDF to Excel (auto-generates output filename)
python pdf_to_excel.py sensor_data.pdf

# Convert PDF to Excel with specific output filename
python pdf_to_excel.py sensor_data.pdf converted_data.xlsx
```

### Testing PDF Extraction

Use the test script to debug PDF extraction issues:
```bash
python test_pdf_extraction.py your_file.pdf
```

This will show detailed information about what data is being extracted from your PDF.

## Supported Data Format

The converter is designed to extract data from PDFs containing tables with:

- **Time1 Column**: Timestamps in format "MM-DD HH:MM:SS" (e.g., "08-26 18:28:03")
- **Sensor Data Column**: Values with "g" unit (e.g., "-1.82 g", "-1.83 g")

Example table format:
```
Data Records                           Printed on: 2025/08/26 22:51:05

Time1          C1:PJX5202
08-26 18:28:03    -1.82 g
08-26 18:28:04    -1.82 g
08-26 18:28:05    -1.82 g
...
```

## Output Format

The Excel file contains two sheets:

### 1. Sensor_Data Sheet
- **Time1**: Full timestamp (YYYY-MM-DD HH:MM:SS)
- **C1_PJX5202_g**: Sensor values in grams
- **Date**: Date only
- **Time_Only**: Time only
- **Hour, Minute, Second**: Time components
- **Abs_Value**: Absolute value of sensor reading
- **Value_Change**: Change from previous reading
- **Running_Average**: 5-point moving average

### 2. Summary Sheet
- Count of data points
- Statistical measures (mean, std dev, min, max, range)
- First and last reading timestamps

## Troubleshooting

### No Data Extracted
1. **Check PDF format**: Ensure the PDF contains text (not just images)
2. **Run test script**: Use `test_pdf_extraction.py` to see what's being extracted
3. **Check data format**: Verify your PDF matches the expected format

### Installation Issues
1. **Update pip**: `python -m pip install --upgrade pip`
2. **Use virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

### GUI Not Working
- Ensure PySide6 is installed: `pip install PySide6`
- Try command line mode as fallback

## Development

### Adding New PDF Formats
To support different PDF formats, modify the regex patterns in the `convert_pdf_to_excel` method:

```python
# Add new pattern for your specific format
new_pattern = r'your_regex_pattern_here'
new_matches = re.findall(new_pattern, line)
```

### Testing
Run the test extraction script to debug new formats:
```bash
python test_pdf_extraction.py sample.pdf
```

## License

This project is open source. Feel free to modify and distribute.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Run the test script to diagnose PDF extraction issues
3. Ensure your PDF format matches the expected structure
