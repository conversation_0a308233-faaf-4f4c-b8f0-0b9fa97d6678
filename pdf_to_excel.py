import sys
import os
import PyPDF2
import pandas as pd
import re
from datetime import datetime
import openpyxl
from PySide6.QtWidgets import QApplication, QFileDialog, QMessageBox, QProgressDialog
from PySide6.QtCore import Qt, QThread, QObject, Signal
from pathlib import Path

def extract_text_from_pdf(pdf_path):
    """
    Extract text from PDF using multiple methods for better compatibility
    """
    text_content = []

    try:
        # Method 1: PyPDF2 (primary method)
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text = page.extract_text()
                if text.strip():
                    text_content.append(text)

        if text_content:
            return text_content

    except Exception as e:
        print(f"PyPDF2 extraction failed: {e}")

    # Method 2: Try alternative extraction if PyPDF2 fails
    try:
        import pdfplumber
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text and text.strip():
                    text_content.append(text)

        if text_content:
            return text_content

    except ImportError:
        print("pdfplumber not available. Install with: pip install pdfplumber")
    except Exception as e:
        print(f"pdfplumber extraction failed: {e}")

    # If all methods fail, return empty list
    return text_content


class PDFConverterWorker(QObject):
    """Worker thread for PDF conversion to avoid blocking the GUI"""
    progress_updated = Signal(int)
    status_updated = Signal(str)
    conversion_finished = Signal(bool, str, object)  # success, message, dataframe

    def __init__(self, pdf_path, excel_path):
        super().__init__()
        self.pdf_path = pdf_path
        self.excel_path = excel_path

    def convert_pdf_to_excel(self):
        """Convert PDF to Excel in background thread"""
        timestamps = []
        sensor_values = []

        try:
            self.status_updated.emit("Opening PDF file...")

            # Use the improved text extraction function
            page_texts = extract_text_from_pdf(self.pdf_path)
            total_pages = len(page_texts)

            if not page_texts:
                self.conversion_finished.emit(False, "❌ Could not extract text from PDF. The PDF might be image-based or corrupted.", None)
                return

            self.status_updated.emit(f"Processing {total_pages} pages...")

            # Process each page
            for page_num, text in enumerate(page_texts):
                self.status_updated.emit(f"Processing page {page_num + 1} of {total_pages}...")
                self.progress_updated.emit(int((page_num / total_pages) * 50))  # First 50% for reading

                # Split text into lines
                lines = text.split('\n')

                # Process each line to extract data
                for line in lines:
                    line = line.strip()

                    # Skip empty lines and headers
                    if not line or 'Time1' in line or 'Data Records' in line or 'Printed on:' in line or 'C1:PJX5202' in line:
                        continue

                    # Debug: Print line being processed (uncomment for debugging)
                    # print(f"Processing line: '{line}'")

                    # Primary pattern to match timestamp and sensor value in table format
                    # Matches lines like: "08-26 18:28:03    -1.82 g"
                    pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
                    matches = re.findall(pattern, line)

                    if matches:
                        for match in matches:
                            timestamp_str = match[0]
                            sensor_value = float(match[1])
                            full_timestamp = f"2025-{timestamp_str}"
                            timestamps.append(full_timestamp)
                            sensor_values.append(sensor_value)
                    else:
                        # Alternative pattern for lines with more spacing or different formatting
                        # This handles cases where there might be extra whitespace or formatting
                        alt_pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
                        alt_matches = re.findall(alt_pattern, line)

                        if alt_matches:
                            for match in alt_matches:
                                timestamp_str = match[0]
                                sensor_value = float(match[1])
                                full_timestamp = f"2025-{timestamp_str}"
                                timestamps.append(full_timestamp)
                                sensor_values.append(sensor_value)
                        else:
                            # Third pattern for cases where timestamp and value might be separated by tabs or multiple spaces
                            tab_pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*\t*\s*(-?\d+\.\d+)\s*g'
                            tab_matches = re.findall(tab_pattern, line)

                            if tab_matches:
                                for match in tab_matches:
                                    timestamp_str = match[0]
                                    sensor_value = float(match[1])
                                    full_timestamp = f"2025-{timestamp_str}"
                                    timestamps.append(full_timestamp)
                                    sensor_values.append(sensor_value)

            # Create DataFrame
            if timestamps and sensor_values:
                self.status_updated.emit("Creating Excel file...")
                self.progress_updated.emit(60)

                df = pd.DataFrame({
                    'Time1': timestamps,
                    'C1_PJX5202_g': sensor_values
                })

                # Convert timestamp to datetime format
                df['Time1'] = pd.to_datetime(df['Time1'], format='%Y-%m-%d %H:%M:%S')
                df = df.sort_values('Time1').reset_index(drop=True)

                # Add additional analysis columns
                df['Date'] = df['Time1'].dt.date
                df['Time_Only'] = df['Time1'].dt.time
                df['Hour'] = df['Time1'].dt.hour
                df['Minute'] = df['Time1'].dt.minute
                df['Second'] = df['Time1'].dt.second

                # Add data analysis columns
                df['Abs_Value'] = abs(df['C1_PJX5202_g'])
                df['Value_Change'] = df['C1_PJX5202_g'].diff()
                df['Running_Average'] = df['C1_PJX5202_g'].rolling(window=5, min_periods=1).mean()

                self.progress_updated.emit(80)
                self.status_updated.emit("Formatting Excel file...")

                # Save to Excel with formatting
                with pd.ExcelWriter(self.excel_path, engine='openpyxl') as writer:
                    # Main data sheet
                    df.to_excel(writer, sheet_name='Sensor_Data', index=False)

                    # Summary statistics sheet
                    summary_stats = {
                        'Statistic': ['Count', 'Mean', 'Std Dev', 'Min', 'Max', 'Range', 'First Reading', 'Last Reading'],
                        'Value': [
                            len(df),
                            df['C1_PJX5202_g'].mean(),
                            df['C1_PJX5202_g'].std(),
                            df['C1_PJX5202_g'].min(),
                            df['C1_PJX5202_g'].max(),
                            df['C1_PJX5202_g'].max() - df['C1_PJX5202_g'].min(),
                            df['Time1'].min(),
                            df['Time1'].max()
                        ]
                    }

                    summary_df = pd.DataFrame(summary_stats)
                    summary_df.to_excel(writer, sheet_name='Summary', index=False)

                    # Format the main sheet
                    worksheet = writer.sheets['Sensor_Data']

                    # Auto-adjust column widths
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter

                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass

                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width

                self.progress_updated.emit(100)

                success_message = (
                    f"✅ Successfully converted PDF to Excel!\n\n"
                    f"📊 Total data points: {len(df):,}\n"
                    f"📅 Date range: {df['Time1'].min()} to {df['Time1'].max()}\n"
                    f"📈 Sensor value range: {df['C1_PJX5202_g'].min():.2f}g to {df['C1_PJX5202_g'].max():.2f}g\n"
                    f"📊 Average value: {df['C1_PJX5202_g'].mean():.3f}g\n"
                    f"💾 Excel file saved as: {self.excel_path}"
                )

                self.conversion_finished.emit(True, success_message, df)

            else:
                self.conversion_finished.emit(False, "❌ No sensor data found in the PDF. Please check the PDF format.",
                                              None)

        except Exception as e:
            self.conversion_finished.emit(False, f"❌ Error processing PDF: {str(e)}", None)


class PDFToExcelConverter:
    """Main converter class with GUI file selection"""

    def __init__(self):
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication(sys.argv)

        self.progress_dialog = None
        self.worker_thread = None
        self.worker = None

    def select_pdf_file(self):
        """Open file dialog to select PDF file"""
        file_dialog = QFileDialog()
        file_dialog.setWindowTitle("Select PDF File to Convert")
        file_dialog.setNameFilter("PDF files (*.pdf)")
        file_dialog.setFileMode(QFileDialog.ExistingFile)

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                return selected_files[0]
        return None

    def generate_excel_path(self, pdf_path):
        """Generate Excel file path with same name and location as PDF"""
        pdf_file = Path(pdf_path)
        excel_path = pdf_file.with_suffix('.xlsx')
        return str(excel_path)

    def show_progress_dialog(self):
        """Show progress dialog during conversion"""
        self.progress_dialog = QProgressDialog("Converting PDF to Excel...", "Cancel", 0, 100)
        self.progress_dialog.setWindowTitle("PDF Converter")
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setAutoClose(False)
        self.progress_dialog.setAutoReset(False)
        self.progress_dialog.show()

    def update_progress(self, value):
        """Update progress dialog"""
        if self.progress_dialog:
            self.progress_dialog.setValue(value)

    def update_status(self, message):
        """Update progress dialog status"""
        if self.progress_dialog:
            self.progress_dialog.setLabelText(message)

    def on_conversion_finished(self, success, message, dataframe):
        """Handle conversion completion"""
        if self.progress_dialog:
            self.progress_dialog.hide()

        # Show result message
        msg_box = QMessageBox()
        if success:
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setWindowTitle("Conversion Successful")
            msg_box.setText(message)

            # Ask if user wants to open the Excel file
            msg_box.setStandardButtons(QMessageBox.Open | QMessageBox.Close)
            msg_box.setDefaultButton(QMessageBox.Open)

            result = msg_box.exec()
            if result == QMessageBox.Open:
                # Try to open the Excel file
                try:
                    import subprocess
                    import platform

                    excel_path = message.split("💾 Excel file saved as: ")[1]

                    if platform.system() == "Windows":
                        os.startfile(excel_path)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.call(["open", excel_path])
                    else:  # Linux
                        subprocess.call(["xdg-open", excel_path])

                except Exception as e:
                    QMessageBox.warning(None, "Warning", f"Could not open Excel file automatically: {str(e)}")
        else:
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("Conversion Failed")
            msg_box.setText(message)
            msg_box.exec()

        # Clean up worker thread
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait()

    def convert_pdf(self):
        """Main conversion method with GUI"""
        # Select PDF file
        pdf_path = self.select_pdf_file()

        if not pdf_path:
            QMessageBox.information(None, "No File Selected", "No PDF file was selected. Conversion cancelled.")
            return

        # Generate Excel output path
        excel_path = self.generate_excel_path(pdf_path)

        # Check if Excel file already exists
        if os.path.exists(excel_path):
            reply = QMessageBox.question(
                None,
                "File Exists",
                f"Excel file '{os.path.basename(excel_path)}' already exists.\n\nDo you want to overwrite it?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                QMessageBox.information(None, "Conversion Cancelled", "Conversion was cancelled by user.")
                return

        # Show progress dialog
        self.show_progress_dialog()

        # Create worker thread for conversion
        self.worker_thread = QThread()
        self.worker = PDFConverterWorker(pdf_path, excel_path)
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.status_updated.connect(self.update_status)
        self.worker.conversion_finished.connect(self.on_conversion_finished)
        self.worker_thread.started.connect(self.worker.convert_pdf_to_excel)

        # Handle cancel button
        self.progress_dialog.canceled.connect(self.cancel_conversion)

        # Start conversion
        self.worker_thread.start()

    def cancel_conversion(self):
        """Cancel the conversion process"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait()

        if self.progress_dialog:
            self.progress_dialog.hide()

        QMessageBox.information(None, "Conversion Cancelled", "PDF to Excel conversion was cancelled.")


def main():
    """Main function to run the converter"""
    print("🚀 PDF to Excel Converter with GUI")
    print("📋 Required libraries:")
    print("   pip install PyPDF2 pandas openpyxl PySide6")
    print("\n" + "=" * 50)

    # Check if running in GUI mode
    try:
        converter = PDFToExcelConverter()
        converter.convert_pdf()

        # Keep the application running until all dialogs are closed
        converter.app.exec()

    except ImportError as e:
        print(f"❌ Missing required library: {e}")
        print("Please install required libraries:")
        print("pip install PyPDF2 pandas openpyxl PySide6")
    except Exception as e:
        print(f"❌ Error: {e}")


# Command line version (fallback)
def convert_pdf_command_line(pdf_path, excel_path):
    """Command line version without GUI"""
    timestamps = []
    sensor_values = []

    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            print(f"Processing PDF with {len(pdf_reader.pages)} pages...")

            for page_num, page in enumerate(pdf_reader.pages):
                print(f"Processing page {page_num + 1}...")

                text = page.extract_text()
                lines = text.split('\n')

                for line in lines:
                    line = line.strip()

                    if not line or 'Time1' in line or 'Data Records' in line or 'Printed on:' in line or 'C1:PJX5202' in line:
                        continue

                    # Primary pattern to match timestamp and sensor value
                    pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
                    matches = re.findall(pattern, line)

                    if matches:
                        for match in matches:
                            timestamp_str = match[0]
                            sensor_value = float(match[1])
                            full_timestamp = f"2025-{timestamp_str}"
                            timestamps.append(full_timestamp)
                            sensor_values.append(sensor_value)
                    else:
                        # Alternative pattern for different formatting
                        tab_pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*\t*\s*(-?\d+\.\d+)\s*g'
                        tab_matches = re.findall(tab_pattern, line)

                        if tab_matches:
                            for match in tab_matches:
                                timestamp_str = match[0]
                                sensor_value = float(match[1])
                                full_timestamp = f"2025-{timestamp_str}"
                                timestamps.append(full_timestamp)
                                sensor_values.append(sensor_value)

        if timestamps and sensor_values:
            df = pd.DataFrame({
                'Time1': timestamps,
                'C1_PJX5202_g': sensor_values
            })

            df['Time1'] = pd.to_datetime(df['Time1'], format='%Y-%m-%d %H:%M:%S')
            df = df.sort_values('Time1').reset_index(drop=True)
            df['Date'] = df['Time1'].dt.date
            df['Time_Only'] = df['Time1'].dt.time

            df.to_excel(excel_path, index=False)
            print(f"✅ Successfully converted! Data points: {len(df)}")
            print(f"📅 Date range: {df['Time1'].min()} to {df['Time1'].max()}")
            print(f"📈 Sensor value range: {df['C1_PJX5202_g'].min():.2f}g to {df['C1_PJX5202_g'].max():.2f}g")
            print(f"💾 Excel file saved as: {excel_path}")

        else:
            print("❌ No data found in the PDF.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    # Try GUI mode first, fallback to command line if needed
    if len(sys.argv) > 1:
        # Command line mode with file arguments
        pdf_file = sys.argv[1]
        excel_file = sys.argv[2] if len(sys.argv) > 2 else str(Path(pdf_file).with_suffix('.xlsx'))
        convert_pdf_command_line(pdf_file, excel_file)
    else:
        # GUI mode
        main()