#!/usr/bin/env python3
"""
Minimal test to isolate the crash issue
Tests only the first page to identify the exact problem
"""

import sys
import os
import warnings

# Suppress all warnings
warnings.filterwarnings("ignore")

def test_basic_imports():
    """Test if basic imports work"""
    print("🧪 Testing imports...")
    
    try:
        import pandas
        print("✅ pandas OK")
    except Exception as e:
        print(f"❌ pandas failed: {e}")
        return False
    
    try:
        import pdfplumber
        print("✅ pdfplumber OK")
        return True
    except Exception as e:
        print(f"⚠️  pdfplumber failed: {e}")
        try:
            import PyPDF2
            print("✅ PyPDF2 OK")
            return True
        except Exception as e2:
            print(f"❌ PyPDF2 failed: {e2}")
            return False

def test_file_access(pdf_path):
    """Test basic file access"""
    print(f"📁 Testing file access: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print("❌ File does not exist")
        return False
    
    try:
        size = os.path.getsize(pdf_path)
        print(f"✅ File size: {size:,} bytes")
    except Exception as e:
        print(f"❌ Cannot get file size: {e}")
        return False
    
    try:
        with open(pdf_path, 'rb') as f:
            first_bytes = f.read(100)
        print(f"✅ Can read file, first bytes: {first_bytes[:20]}")
        return True
    except Exception as e:
        print(f"❌ Cannot read file: {e}")
        return False

def test_pdf_open(pdf_path):
    """Test opening PDF without extracting text"""
    print("📖 Testing PDF open...")
    
    try:
        import pdfplumber
        with pdfplumber.open(pdf_path) as pdf:
            page_count = len(pdf.pages)
            print(f"✅ pdfplumber: {page_count} pages")
            return True, 'pdfplumber'
    except Exception as e:
        print(f"⚠️  pdfplumber open failed: {e}")
    
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file, strict=False)
            page_count = len(pdf_reader.pages)
            print(f"✅ PyPDF2: {page_count} pages")
            return True, 'PyPDF2'
    except Exception as e:
        print(f"❌ PyPDF2 open failed: {e}")
        return False, None

def test_single_page_extraction(pdf_path, lib_type):
    """Test extracting text from just the first page"""
    print("📄 Testing single page extraction...")
    
    try:
        if lib_type == 'pdfplumber':
            import pdfplumber
            with pdfplumber.open(pdf_path) as pdf:
                if len(pdf.pages) > 0:
                    page = pdf.pages[0]
                    text = page.extract_text()
                    if text:
                        print(f"✅ Extracted {len(text)} characters from page 1")
                        print(f"📝 First 100 chars: {repr(text[:100])}")
                        return text
                    else:
                        print("⚠️  No text extracted")
                        return ""
        else:  # PyPDF2
            import PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file, strict=False)
                if len(pdf_reader.pages) > 0:
                    page = pdf_reader.pages[0]
                    text = page.extract_text()
                    if text:
                        print(f"✅ Extracted {len(text)} characters from page 1")
                        print(f"📝 First 100 chars: {repr(text[:100])}")
                        return text
                    else:
                        print("⚠️  No text extracted")
                        return ""
        
        return ""
        
    except Exception as e:
        print(f"❌ Text extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_data_extraction(text):
    """Test extracting data from text"""
    print("🔍 Testing data extraction...")
    
    if not text:
        print("❌ No text to process")
        return False
    
    try:
        import re
        
        lines = text.split('\n')
        print(f"📄 Text has {len(lines)} lines")
        
        # Show first few lines
        print("📋 First 5 lines:")
        for i, line in enumerate(lines[:5]):
            print(f"  {i+1}: {repr(line)}")
        
        # Test regex pattern
        pattern = r'(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(-?\d+\.\d+)\s*g'
        
        data_found = 0
        for line in lines:
            matches = re.findall(pattern, line.strip())
            if matches:
                data_found += len(matches)
                if data_found <= 3:  # Show first 3
                    for match in matches:
                        print(f"📊 Found: {match[0]} -> {match[1]}g")
        
        print(f"✅ Found {data_found} data points")
        return data_found > 0
        
    except Exception as e:
        print(f"❌ Data extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataframe_creation():
    """Test creating a small DataFrame"""
    print("📊 Testing DataFrame creation...")
    
    try:
        import pandas as pd
        
        # Create test data
        test_data = [
            {'Time1': '2025-08-26 18:28:03', 'C1_PJX5202_g': -1.82},
            {'Time1': '2025-08-26 18:28:04', 'C1_PJX5202_g': -1.83},
            {'Time1': '2025-08-26 18:28:05', 'C1_PJX5202_g': -1.84}
        ]
        
        df = pd.DataFrame(test_data)
        print(f"✅ Created DataFrame with {len(df)} rows")
        
        # Test datetime conversion
        df['Time1'] = pd.to_datetime(df['Time1'])
        print("✅ Datetime conversion OK")
        
        return True
        
    except Exception as e:
        print(f"❌ DataFrame creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Minimal PDF Test - Crash Isolation")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python minimal_test.py <pdf_file>")
        return
    
    pdf_file = sys.argv[1]
    
    # Test 1: Imports
    if not test_basic_imports():
        print("❌ Import test failed")
        return
    
    # Test 2: File access
    if not test_file_access(pdf_file):
        print("❌ File access test failed")
        return
    
    # Test 3: PDF open
    can_open, lib_type = test_pdf_open(pdf_file)
    if not can_open:
        print("❌ PDF open test failed")
        return
    
    # Test 4: Single page extraction
    text = test_single_page_extraction(pdf_file, lib_type)
    if text is None:
        print("❌ Text extraction test failed")
        return
    
    # Test 5: Data extraction
    if not test_data_extraction(text):
        print("❌ Data extraction test failed")
        return
    
    # Test 6: DataFrame creation
    if not test_dataframe_creation():
        print("❌ DataFrame test failed")
        return
    
    print("\n🎉 All tests passed!")
    print("The PDF format appears to be compatible.")
    print("\nNext step: Try the safe converter with limited pages:")
    print(f"python safe_pdf_converter.py {pdf_file} 10")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error in test: {e}")
        import traceback
        traceback.print_exc()
